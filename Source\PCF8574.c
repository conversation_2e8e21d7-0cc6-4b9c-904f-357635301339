#include "Includes.h"
#include "Xbee3.h"
#include "EEpromVari.h"
/*******************************************************************************
*******************************************************************************/
INT8U Init8574(INT8U ChipAddr){
  INT8U TempFlag=m_NO;
  LcdI2CStart1();
  TempFlag=LcdSendI2CByte1(ChipAddr);
  TempFlag=LcdSendI2CByte1(0xFF);
  LcdI2CStop1();
  return TempFlag;
}
/*******************************************************************************
*******************************************************************************/
void ReadFrom8574(void){
  INT8U lu8v_CountBit;
  INT8U lu8v_SendData;
  switch (INT8U_LcdI2cType){
  case m_I2C4W:
    lu8v_SendData = m_ReadMode4W;
    break;
  case m_I2C4WA:
    lu8v_SendData = m_ReadMode4WA;
    break;
  }
  INT8U_KeyStatus = m_KeyNull;
  LcdI2CStart1();
  for (lu8v_CountBit = 0;lu8v_CountBit < 8;lu8v_CountBit++) {
    if ((lu8v_SendData & 0x80) != 0) {
      m_SetLcdSDA;
    }
    else{
      m_ClrLcdSDA;
    }
    m_Delay8;
    m_SetLcdSCK;
    lu8v_SendData = lu8v_SendData << 1;
    m_Delay8;
    m_ClrLcdSCK;
    m_Delay4;m_Delay2;
  }
  m_SetLcdSDA;
  m_SetLcdSDAIN;
  m_Delay8;
  m_SetLcdSCK;
  m_Delay8;m_Delay8;
  if (m_LcdIn == 0){
    m_ClrLcdSCK;
    m_SetLcdSDA;
    m_SetLcdSDAOUT;
    for (lu8v_CountBit = 0;lu8v_CountBit < 8;lu8v_CountBit++) {
      m_Delay10;
      m_SetLcdSDAIN;
      m_Delay4;
      INT8U_KeyStatus <<= 1;
      m_SetLcdSCK;
      m_Delay10;m_Delay10;
      if (m_LcdIn != 0)   {
        INT8U_KeyStatus |= BIT0;
      }
      else{
        INT8U_KeyStatus &= ~BIT0;
      }
      m_ClrLcdSCK;
      m_SetLcdSDA;
      m_SetLcdSDAOUT;
    }
    m_Delay10;m_Delay10;
    m_ClrLcdSDA;
    //ASK
    m_Delay10;
    m_SetLcdSCK;
    m_Delay10;
    m_ClrLcdSCK;
    m_Delay10;m_Delay10;
    INT8U_KeyStatus &= m_KeyNull;
    INT8U_UpdateLCD = 0;
  }
  else{
    m_ClrLcdSCK;
    m_Delay10;
    m_SetLcdSDAOUT;
    m_Delay10;m_Delay8;
    m_ClrLcdSDA;
    INT8U_LcdI2cErrFlag = m_NO;
    INT8U_UpdateLCD++;
    if(INT8U_UpdateLCD > 100){
      INT8U_KeyFuncIndex = m_MenuTop;
      INT8U_Sim[0] = 0;
      INT8U_Sim[1] = 0;
      INT8U_Sim[2] = 0;
      INT8U_UpdateLCD = 101;
    }
  }
  LcdI2CStop1();
}
