/*******************************************************************************
*                     Driver for I2C
*******************************************************************************/
#include "Includes.h"
/*******************************************************************************
                     LCD  Start Send
*******************************************************************************/
void LcdI2CStart(void){
  m_SetLcdSDAOUT;
  m_SetLcdSDA;
  m_SetLcdSCK;
  m_Delay10;
  m_ClrLcdSDA;
  m_Delay10;
  m_ClrLcdSCK;
  m_Delay10;
}

/*******************************************************************************
                  Send byte as Master
*******************************************************************************/
void LcdSendI2CByte(INT8U SendData){
  INT8U CountBit;
  m_SetLcdSDAOUT;
  for (CountBit = 0;CountBit < 8;CountBit++){
    if ((SendData & 0x80) != 0){
      m_SetLcdSDA;
    }
    else{
      m_ClrLcdSDA;
    }
    m_Delay2;
    m_SetLcdSCK;
    SendData = SendData << 1;
    m_Delay2;
    m_ClrLcdSCK;
    m_Delay2;
  }
  m_Delay2;
  m_SetLcdSDA;
  m_SetLcdSDAIN;
  m_Delay2;
  m_SetLcdSCK;
  m_Delay4;
  if (m_LcdIn != 0){
    INT8U_LcdI2cErrFlag = m_NO;
  }
  m_Delay2;
  m_ClrLcdSCK;
  m_SetLcdSDAOUT;
  m_Delay2;
  INT8U_Colindex++;
}
/*******************************************************************************
*                     LCD  Stop Send
*******************************************************************************/
void LcdI2CStop(void){
  m_Delay10;
  m_SetLcdSDAOUT;
  m_ClrLcdSDA;
  m_SetLcdSCK;
  m_Delay10;
  m_SetLcdSDA;
  m_Delay10;
}
/*******************************************************************************
������I2C
*******************************************************************************/
void LcdI2CStart1(void){
  m_SetLcdSDAOUT;
  m_SetLcdSDA;
  m_SetLcdSCK;
  m_Delay10;
  m_ClrLcdSDA;
  m_Delay8;
  m_ClrLcdSCK;
  m_Delay10;
}
/*******************************************************************************
*******************************************************************************/
INT8U LcdSendI2CByte1(INT8U SendData){
  INT8U CountBit;
  INT8U TempErrFlag=m_NO;
  for (CountBit = 0;CountBit < 8;CountBit++){
    if ((SendData & 0x80) != 0){
      m_SetLcdSDA;
    }
    else{
      m_ClrLcdSDA;
    }
    m_Delay8;
    m_SetLcdSCK;
    SendData = SendData << 1;
    m_Delay8;
    m_ClrLcdSCK;
    m_Delay4;m_Delay2;
  }
  m_SetLcdSDA;
  m_SetLcdSDAIN;
  m_Delay8;
  m_SetLcdSCK;
  m_Delay8;m_Delay8;m_Delay8;
  if (m_LcdIn != 0)   {
    TempErrFlag = m_NO;
  }
  else{
    TempErrFlag = m_YES;
  }
  m_ClrLcdSCK;
  m_SetLcdSDAOUT;
  m_Delay10;m_Delay8;
  return TempErrFlag;
}
/*******************************************************************************
*******************************************************************************/
void LcdI2CStop1(void){
  m_ClrLcdSDA;
  m_Delay4;
  m_SetLcdSCK;
  m_Delay10;
  m_SetLcdSDA;
}